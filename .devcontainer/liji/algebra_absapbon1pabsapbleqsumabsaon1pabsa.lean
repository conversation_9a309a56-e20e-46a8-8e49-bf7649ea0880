import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Ring.Abs
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.FieldSimp
import Mathlib.Tactic.Ring

theorem abs_div_ineq (a b : ℝ) :
  |a + b| / (1 + |a + b|) ≤ |a| / (1 + |a|) + |b| / (1 + |b|) := by
  -- Define substitutions x = |a|, y = |b|
  let x := |a|
  let y := |b|
  -- Define function g(t) = t/(1+t)
  let g : ℝ → ℝ := fun t => t / (1 + t)

  -- Step 1: Show x, y ≥ 0
  have hx : 0 ≤ x := abs_nonneg a
  have hy : 0 ≤ y := abs_nonneg b

  -- Step 2: Direct algebraic proof by clearing denominators
  -- Show: |a+b|/(1+|a+b|) ≤ |a|/(1+|a|) + |b|/(1+|b|)
  -- Multiply by common denominator: (1+|a+b|)(1+|a|)(1+|b|)
  have h1 : 0 < 1 + |a + b| := by linarith [abs_nonneg (a + b)]
  have h2 : 0 < 1 + x := by l<PERSON><PERSON> [hx]
  have h3 : 0 < 1 + y := by linarith [hy]

  -- Use triangle inequality: |a+b| ≤ |a| + |b|
  have triangle : |a + b| ≤ x + y := abs_add a b

  -- Use triangle inequality and monotonicity of g(t) = t/(1+t)
  -- First show that g is monotone on [0,∞)
  have g_mono : ∀ s t : ℝ, 0 ≤ s → s ≤ t → g s ≤ g t := by
    intros s t hs hst
    unfold g
    -- Use div_le_div_iff₀ to compare fractions
    rw [div_le_div_iff₀ (add_pos_of_pos_of_nonneg zero_lt_one hs)
                        (add_pos_of_pos_of_nonneg zero_lt_one (le_trans hs hst))]
    -- This reduces to: s * (1 + t) ≤ t * (1 + s)
    ring_nf
    -- Simplifies to: s ≤ t, which is our assumption
    exact hst

  -- Apply monotonicity: g(|a+b|) ≤ g(|a|+|b|)
  have step1 : g (|a + b|) ≤ g (x + y) := by
    apply g_mono
    · exact abs_nonneg (a + b)
    · exact triangle

  -- Show that g(x+y) ≤ g(x) + g(y) for x,y ≥ 0
  have step2 : g (x + y) ≤ g x + g y := by
    unfold g
    -- Clear denominators using div_le_iff₀
    rw [div_le_iff₀ (add_pos_of_pos_of_nonneg zero_lt_one (add_nonneg hx hy))]
    rw [add_div, div_mul_eq_mul_div, div_mul_eq_mul_div]
    rw [mul_add, add_mul]
    -- After clearing denominators, this reduces to showing xy ≥ 0
    ring_nf
    exact mul_nonneg hx hy

  -- Combine the steps
  exact le_trans step1 step2
